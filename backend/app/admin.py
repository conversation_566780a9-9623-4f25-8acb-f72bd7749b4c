from django.contrib import admin
from .models import (
    Profile, Category, Hero, About, Experience,
    Project, Blog, Contact
)

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'role', 'email', 'created_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['name', 'email', 'role']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'type']
    list_filter = ['type']
    search_fields = ['name']

@admin.register(Hero)
class HeroAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'subtitle', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'subtitle']
    readonly_fields = ['created_at', 'updated_at']
    
    def save_model(self, request, obj, form, change):
        # If setting this hero as active, deactivate all others
        if obj.is_active:
            Hero.objects.exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

@admin.register(About)
class AboutAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'years_of_experience', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    def save_model(self, request, obj, form, change):
        # If setting this about as active, deactivate all others
        if obj.is_active:
            About.objects.exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

@admin.register(Experience)
class ExperienceAdmin(admin.ModelAdmin):
    list_display = ['id', 'company_name', 'role', 'start_date', 'end_date', 'is_active']
    list_filter = ['is_active', 'start_date', 'end_date']
    search_fields = ['company_name', 'role', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-start_date']

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'category', 'is_featured', 'created_at']
    list_filter = ['is_featured', 'category', 'created_at']
    search_fields = ['title', 'description', 'short_description']
    readonly_fields = ['created_at', 'updated_at']
    autocomplete_fields = ['category']
    ordering = ['-created_at']

@admin.register(Blog)
class BlogAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'slug', 'category', 'is_featured', 'count', 'created_at']
    list_filter = ['is_featured', 'category', 'created_at']
    search_fields = ['title', 'content', 'excerpt']
    readonly_fields = ['created_at', 'updated_at', 'count']
    prepopulated_fields = {'slug': ('title',)}
    autocomplete_fields = ['category']
    ordering = ['-created_at']

@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'email', 'subject', 'preferred_method', 'created_at']
    list_filter = ['preferred_method', 'created_at']
    search_fields = ['name', 'email', 'subject', 'message']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def has_add_permission(self, request):
        # Prevent adding contacts from admin (they come from frontend)
        return False
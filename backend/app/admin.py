from django.contrib import admin
from .models import (
    Profile, Category, Hero, About, Experience,
    Project, Blog, Contact, SocialLink, TypingText,
    Skill, ExperienceTechnology, ProjectTechnology,
    ProjectGalleryImage, BlogTag, BlogImage, BlogCodeSnippet
)

# Inline admin classes
class SocialLinkInline(admin.TabularInline):
    model = SocialLink
    extra = 1
    fields = ['platform', 'url', 'order']

class TypingTextInline(admin.TabularInline):
    model = TypingText
    extra = 1
    fields = ['text', 'order']

class SkillInline(admin.TabularInline):
    model = Skill
    extra = 1
    fields = ['name', 'level', 'order']

class ExperienceTechnologyInline(admin.TabularInline):
    model = ExperienceTechnology
    extra = 1
    fields = ['name', 'order']

class ProjectTechnologyInline(admin.TabularInline):
    model = ProjectTechnology
    extra = 1
    fields = ['name', 'order']

class ProjectGalleryImageInline(admin.TabularInline):
    model = ProjectGalleryImage
    extra = 1
    fields = ['image', 'caption', 'order']

class BlogTagInline(admin.TabularInline):
    model = BlogTag
    extra = 1
    fields = ['name', 'order']

class BlogImageInline(admin.TabularInline):
    model = BlogImage
    extra = 1
    fields = ['image', 'caption', 'alt_text', 'is_inline', 'order']

class BlogCodeSnippetInline(admin.StackedInline):  # Using StackedInline for better code editing
    model = BlogCodeSnippet
    extra = 1  # Start with 1 for easier adding
    fields = [
        ('title', 'language'),
        'code',
        'description',
        ('filename', 'is_highlighted', 'order')
    ]
    classes = ['collapse']  # Make it collapsible to save space

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        # Add custom CSS class for better code editing
        formset.form.base_fields['code'].widget.attrs.update({
            'class': 'vLargeTextField',
            'rows': 10,
            'style': 'font-family: monospace; font-size: 12px;'
        })
        return formset

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'role', 'email', 'created_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['name', 'email', 'role']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [SocialLinkInline]

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'type']
    list_filter = ['type']
    search_fields = ['name']

@admin.register(Hero)
class HeroAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'subtitle', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'subtitle']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [TypingTextInline]

    def save_model(self, request, obj, form, change):
        # If setting this hero as active, deactivate all others
        if obj.is_active:
            Hero.objects.exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

@admin.register(About)
class AboutAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'years_of_experience', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [SkillInline]

    def save_model(self, request, obj, form, change):
        # If setting this about as active, deactivate all others
        if obj.is_active:
            About.objects.exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)

@admin.register(Experience)
class ExperienceAdmin(admin.ModelAdmin):
    list_display = ['id', 'company_name', 'role', 'start_date', 'end_date', 'is_active']
    list_filter = ['is_active', 'start_date', 'end_date']
    search_fields = ['company_name', 'role', 'description']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-start_date']
    inlines = [ExperienceTechnologyInline]

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'category', 'is_featured', 'created_at']
    list_filter = ['is_featured', 'category', 'created_at']
    search_fields = ['title', 'description', 'short_description']
    readonly_fields = ['created_at', 'updated_at']
    autocomplete_fields = ['category']
    ordering = ['-created_at']
    inlines = [ProjectTechnologyInline, ProjectGalleryImageInline]

@admin.register(Blog)
class BlogAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'slug', 'category', 'difficulty_level', 'is_featured', 'is_published', 'reading_time', 'created_at']
    list_filter = ['is_featured', 'is_published', 'difficulty_level', 'category', 'created_at']
    search_fields = ['title', 'content', 'excerpt']
    readonly_fields = ['created_at', 'updated_at', 'count']
    prepopulated_fields = {'slug': ('title',)}
    autocomplete_fields = ['category']
    ordering = ['-created_at']
    inlines = [BlogTagInline, BlogImageInline, BlogCodeSnippetInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'category', 'excerpt')
        }),
        ('Content', {
            'fields': ('content', 'featured_image')
        }),
        ('Tech Blog Settings', {
            'fields': ('difficulty_level', 'reading_time', 'is_featured', 'is_published', 'published_at'),
            'classes': ['collapse']
        }),
        ('Metadata', {
            'fields': ('count', 'created_at', 'updated_at'),
            'classes': ['collapse']
        })
    )

@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'email', 'subject', 'preferred_method', 'created_at']
    list_filter = ['preferred_method', 'created_at']
    search_fields = ['name', 'email', 'subject', 'message']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def has_add_permission(self, request):
        # Prevent adding contacts from admin (they come from frontend)
        return False
from rest_framework import serializers
from .models import (
    Profile, Category, Hero, About, Experience,
    Project, Blog, Contact, SocialLink, TypingText,
    Skill, ExperienceTechnology, ProjectTechnology,
    ProjectGalleryImage, BlogTag
)

# Serializers for related models
class SocialLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialLink
        fields = ['id', 'platform', 'url', 'order']

class TypingTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = TypingText
        fields = ['id', 'text', 'order']

class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = ['id', 'name', 'level', 'order']

class ExperienceTechnologySerializer(serializers.ModelSerializer):
    class Meta:
        model = ExperienceTechnology
        fields = ['id', 'name', 'order']

class ProjectTechnologySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectTechnology
        fields = ['id', 'name', 'order']

class ProjectGalleryImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectGalleryImage
        fields = ['id', 'image', 'caption', 'order']

class BlogTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = BlogTag
        fields = ['id', 'name', 'order']

# Main model serializers
class ProfileSerializer(serializers.ModelSerializer):
    social_links = SocialLinkSerializer(many=True, read_only=True)

    class Meta:
        model = Profile
        fields = '__all__'

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class HeroSerializer(serializers.ModelSerializer):
    typing_texts = TypingTextSerializer(many=True, read_only=True)

    class Meta:
        model = Hero
        fields = '__all__'

class AboutSerializer(serializers.ModelSerializer):
    skills = SkillSerializer(many=True, read_only=True)

    class Meta:
        model = About
        fields = '__all__'

class ExperienceSerializer(serializers.ModelSerializer):
    technologies = ExperienceTechnologySerializer(many=True, read_only=True)

    class Meta:
        model = Experience
        fields = '__all__'
        
class ProjectListSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    technologies = ProjectTechnologySerializer(many=True, read_only=True)

    class Meta:
        model = Project
        fields = ['id', 'title', 'short_description', 'image',
                 'technologies', 'category', 'is_featured',
                 'live_url', 'github_url', 'created_at']

class ProjectDetailSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    technologies = ProjectTechnologySerializer(many=True, read_only=True)
    gallery_images = ProjectGalleryImageSerializer(many=True, read_only=True)

    class Meta:
        model = Project
        fields = '__all__'

class BlogListSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    tags = BlogTagSerializer(many=True, read_only=True)

    class Meta:
        model = Blog
        fields = ['id', 'title', 'slug', 'excerpt', 'featured_image',
                 'is_featured', 'tags', 'category', 'count', 'created_at']

class BlogDetailSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    tags = BlogTagSerializer(many=True, read_only=True)

    class Meta:
        model = Blog
        fields = '__all__'

class ContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = Contact
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
from django.db import models

class Profile (models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.Char<PERSON><PERSON>(max_length=100, null=True, blank=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    bio = models.TextField(null=True, blank= True)
    avatar = models.ImageField(null=True, blank=True)
    resume = models.FileField(null=True, blank=True)
    social_links = models.J<PERSON><PERSON>ield(default=list, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Category(models.Model):
    name = models.CharField(max_length=50)
    type = models.CharField(max_length=20, choices=(
        ('project', 'Project'), 
        ('blog', 'Blog')
    ))

class Hero (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    subtitle = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    typing_text = models.J<PERSON><PERSON><PERSON>(default=list, blank=True)
    image = models.ImageField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class About (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    image = models.ImageField(null=True, blank=True)
    skills = models.JSONField(default=list, blank=True)
    years_of_experience = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Experience (models.Model):
    id = models.BigAutoField(primary_key=True)
    company_name = models.CharField(max_length=100, null=True, blank=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    technologies = models.JSONField(default=list, blank=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Project (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    short_description = models.TextField(null=True, blank=True)
    image = models.ImageField(null=True, blank=True)
    gallery_images = models.JSONField(null=True, blank=True)
    technologies = models.JSONField(null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True, blank=True)
    live_url = models.URLField(null=True, blank=True)
    github_url = models.URLField(null=True, blank=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Blog (models.Model):
    id = models.BigAutoField(primary_key=True)
    title = models.CharField(max_length=100, null=True, blank=True)
    slug = models.SlugField(null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    excerpt = models.TextField(null=True, blank=True)
    featured_image = models.ImageField(null=True, blank=True)
    is_featured = models.BooleanField(default=False)
    tags = models.JSONField(null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True, blank=True)
    count = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Contact (models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    subject = models.CharField(max_length=100, null=True, blank=True)
    phone = models.CharField(max_length=100, null=True, blank=True)
    preferred_method = models.CharField(max_length=100, null=True, blank=True, choices=(('email', 'Email'), ('phone', 'Phone'), ('whatsapp', 'Whatsapp')))
    message = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

